<template>
  <v-app>
    <!-- Navigation Drawer -->
    <v-navigation-drawer
      v-model="drawer"
      app
      temporary
    >
      <v-list>
        <v-list-item
          prepend-avatar="@/assets/logo.png"
          title="Track Tasks"
          subtitle="Project Task Management"
        />
      </v-list>

      <v-divider />

      <v-list density="compact" nav>
        <v-list-item
          v-for="item in navigationItems"
          :key="item.title"
          :prepend-icon="item.icon"
          :title="item.title"
          :subtitle="item.subtitle"
          :to="item.to"
          :value="item.value"
          class="mb-1"
        >
          <template #append v-if="item.badge">
            <v-badge
              :content="item.badge"
              color="primary"
              inline
            />
          </template>
        </v-list-item>
      </v-list>

      <template #append>
        <div class="pa-4">
          <v-btn
            block
            variant="outlined"
            prepend-icon="mdi-github"
            href="https://github.com"
            target="_blank"
          >
            GitHub
          </v-btn>
        </div>
      </template>
    </v-navigation-drawer>

    <!-- App Bar -->
    <v-app-bar elevation="2" color="primary" density="comfortable">
      <v-app-bar-nav-icon @click="drawer = !drawer" />
      
      <v-app-bar-title class="d-flex align-center">
        <v-icon class="me-3">mdi-format-list-checks</v-icon>
        Track Tasks
      </v-app-bar-title>
      
      <v-spacer />

      <!-- Desktop Navigation -->
      <div class="d-none d-md-flex">
        <v-btn
          v-for="item in navigationItems"
          :key="item.title"
          :to="item.to"
          :variant="$route.path === item.to ? 'elevated' : 'text'"
          :prepend-icon="item.icon"
          class="me-2"
        >
          {{ item.title }}
          <v-badge
            v-if="item.badge"
            :content="item.badge"
            color="white"
            text-color="primary"
            floating
          />
        </v-btn>
      </div>

      <!-- Task Count Badge -->
      <v-chip
        v-if="tasksStore.tasks.length > 0"
        color="white"
        text-color="primary"
        variant="elevated"
        size="small"
        class="ms-2"
      >
        {{ tasksStore.tasks.length }} Tasks
      </v-chip>
    </v-app-bar>
    
    <!-- Main Content -->
    <v-main>
      <router-view />
    </v-main>

    <AppFooter />
  </v-app>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useTasksStore } from '@/stores/tasks'
import AppFooter from '@/components/AppFooter.vue'

const route = useRoute()
const tasksStore = useTasksStore()

const drawer = ref(false)

const navigationItems = computed(() => [
  {
    title: 'Tasks',
    subtitle: 'View and manage tasks',
    icon: 'mdi-format-list-checks',
    to: '/',
    value: 'tasks',
    badge: tasksStore.tasks.length > 0 ? tasksStore.tasks.length : null
  },
  {
    title: 'Upload',
    subtitle: 'Import task data',
    icon: 'mdi-file-upload',
    to: '/upload',
    value: 'upload'
  },
  {
    title: 'Help',
    subtitle: 'Documentation & guides',
    icon: 'mdi-help-circle',
    to: '/help',
    value: 'help'
  }
])

onMounted(() => {
  tasksStore.fetchTasks()
})
</script>
