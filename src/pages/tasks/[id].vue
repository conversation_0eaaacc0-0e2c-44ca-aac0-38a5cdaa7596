<template>
  <v-container class="pa-6" fluid>
    <!-- Loading State -->
    <div v-if="loading" class="d-flex justify-center align-center" style="min-height: 400px;">
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      />
    </div>

    <!-- Error State -->
    <v-alert
      v-else-if="error"
      class="mb-4"
      type="error"
      variant="tonal"
    >
      {{ error }}
    </v-alert>

    <!-- Task Not Found -->
    <v-card v-else-if="!task" class="text-center pa-8" elevation="2">
      <v-icon class="mb-4" color="grey-lighten-2" size="64">
        mdi-file-question-outline
      </v-icon>
      <h2 class="text-h5 mb-4">Task Not Found</h2>
      <p class="text-body-1 text-medium-emphasis mb-6">
        The requested task could not be found in the database.
      </p>
      <v-btn color="primary" @click="$router.push('/')">
        <v-icon start>mdi-arrow-left</v-icon>
        Back to Tasks
      </v-btn>
    </v-card>

    <!-- Task Details -->
    <div v-else>
      <!-- Header with Navigation -->
      <v-row class="mb-4">
        <v-col>
          <div class="d-flex align-center justify-space-between">
            <v-btn
              class="text-none"
              prepend-icon="mdi-arrow-left"
              variant="text"
              @click="$router.push('/')"
            >
              {{ isNewTask ? 'Back to Tasks' : 'Back to Tasks' }}
            </v-btn>

            <div class="d-flex align-center ga-2">
              <v-chip
                :color="getPriorityColor(task.priority)"
                size="small"
                variant="elevated"
              >
                {{ task.priority }} Priority
              </v-chip>
              <v-chip
                :color="getStatusColor(task.status)"
                size="small"
                variant="tonal"
              >
                {{ task.status }}
              </v-chip>

              <!-- Edit Mode Toggle Button -->
              <v-btn
                v-if="!isEditMode && !isNewTask"
                color="primary"
                prepend-icon="mdi-pencil"
                size="small"
                variant="outlined"
                @click="enterEditMode"
              >
                Edit
              </v-btn>

              <!-- Delete Button -->
              <v-btn
                v-if="!isEditMode && !isNewTask"
                color="error"
                prepend-icon="mdi-delete"
                size="small"
                variant="outlined"
                @click="openDeleteDialog"
              >
                Delete Task
              </v-btn>

              <!-- Save/Cancel Buttons in Edit Mode -->
              <div v-else class="d-flex ga-2">
                <v-btn
                  color="success"
                  :loading="editLoading"
                  prepend-icon="mdi-check"
                  size="small"
                  variant="elevated"
                  @click="saveTask"
                >
                  {{ isNewTask ? 'Create Task' : 'Save' }}
                </v-btn>
                <v-btn
                  color="grey"
                  :disabled="editLoading"
                  prepend-icon="mdi-close"
                  size="small"
                  variant="outlined"
                  @click="cancelEdit"
                >
                  Cancel
                </v-btn>
              </div>
            </div>
          </div>
        </v-col>
      </v-row>

      <!-- Task Header Card -->
      <v-card class="mb-6" elevation="2">
        <v-card-title class="d-flex align-start justify-space-between">
          <div class="flex-1-1">
            <div class="d-flex align-center mb-2">
              <v-avatar class="me-3" :color="getPriorityColor(task.priority)" size="40">
                <v-icon color="white">{{ getTypeIcon(task.type) }}</v-icon>
              </v-avatar>
              <div class="flex-1-1">
                <!-- View Mode -->
                <div v-if="!isEditMode">
                  <div class="text-h5 font-weight-bold">{{ task.summary }}</div>
                  <div class="text-body-2 text-medium-emphasis">{{ task.task_id }}</div>
                </div>

                <!-- Edit Mode -->
                <div v-else>
                  <!-- Custom Task ID for New Tasks -->
                  <div v-if="isNewTask" class="mb-3">
                    <v-text-field
                      v-model="editForm.task_id"
                      density="compact"
                      hide-details="auto"
                      label="Task ID"
                      placeholder="Enter custom task ID (e.g., PROJ-123)"
                      prepend-inner-icon="mdi-identifier"
                      :rules="rules.task_id"
                      variant="outlined"
                    />
                    <div class="d-flex align-center mt-1">
                      <v-btn
                        color="primary"
                        prepend-icon="mdi-auto-fix"
                        size="small"
                        variant="text"
                        @click="generateNewTaskId"
                      >
                        Generate ID
                      </v-btn>
                      <v-spacer />
                      <div class="text-caption text-medium-emphasis">
                        Format: letters, numbers, and hyphens only
                      </div>
                    </div>
                  </div>

                  <v-text-field
                    v-model="editForm.summary"
                    class="mb-2"
                    density="compact"
                    hide-details="auto"
                    label="Summary"
                    :rules="rules.summary"
                    variant="outlined"
                  />
                  <div class="text-body-2 text-medium-emphasis">
                    {{ isNewTask ? 'Enter a unique task ID above' : `${task.task_id} (Read-only)` }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <v-menu>
            <template #activator="{ props }">
              <v-btn
                v-bind="props"
                icon="mdi-dots-vertical"
                variant="text"
              />
            </template>
            <v-list>
              <v-list-item @click="updateStatus('Backlog')">
                <v-list-item-title>Move to Backlog</v-list-item-title>
              </v-list-item>
              <v-list-item @click="updateStatus('In Progress')">
                <v-list-item-title>Start Progress</v-list-item-title>
              </v-list-item>
              <v-list-item @click="updateStatus('Done')">
                <v-list-item-title>Mark Done</v-list-item-title>
              </v-list-item>
              <v-list-item @click="updateStatus('Blocked')">
                <v-list-item-title>Mark Blocked</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </v-card-title>

        <v-card-text>
          <v-row>
            <v-col cols="12" md="6">
              <div class="mb-4">
                <h4 class="text-subtitle-1 font-weight-medium mb-2">Task Details</h4>

                <!-- View Mode -->
                <v-table v-if="!isEditMode" density="compact">
                  <tbody>
                    <tr>
                      <td class="font-weight-medium">Type</td>
                      <td>
                        <v-chip size="small" variant="outlined">
                          {{ task.type }}
                        </v-chip>
                      </td>
                    </tr>
                    <tr>
                      <td class="font-weight-medium">Priority</td>
                      <td>
                        <v-chip :color="getPriorityColor(task.priority)" size="small">
                          {{ task.priority }}
                        </v-chip>
                      </td>
                    </tr>
                    <tr>
                      <td class="font-weight-medium">Status</td>
                      <td>
                        <v-chip :color="getStatusColor(task.status)" size="small" variant="tonal">
                          {{ task.status }}
                        </v-chip>
                      </td>
                    </tr>
                    <tr v-if="task.estimated_effort">
                      <td class="font-weight-medium">Estimated Effort</td>
                      <td>{{ task.estimated_effort }}</td>
                    </tr>
                    <tr v-if="task.epic">
                      <td class="font-weight-medium">Epic</td>
                      <td>
                        <v-chip color="primary" size="small" variant="text">
                          {{ task.epic }}
                        </v-chip>
                      </td>
                    </tr>
                  </tbody>
                </v-table>

                <!-- Edit Mode -->
                <div v-else class="d-flex flex-column ga-3">
                  <v-select
                    v-model="editForm.type"
                    density="compact"
                    hide-details
                    :items="typeOptions"
                    label="Type"
                    variant="outlined"
                  />

                  <v-select
                    v-model="editForm.priority"
                    density="compact"
                    hide-details
                    :items="priorityOptions"
                    label="Priority"
                    variant="outlined"
                  />

                  <v-select
                    v-model="editForm.status"
                    density="compact"
                    hide-details
                    :items="statusOptions"
                    label="Status"
                    variant="outlined"
                  />

                  <v-select
                    v-model="editForm.estimated_effort"
                    density="compact"
                    hide-details
                    :items="effortOptions"
                    label="Estimated Effort"
                    variant="outlined"
                  />

                  <v-text-field
                    v-model="editForm.epic"
                    density="compact"
                    hide-details
                    label="Epic"
                    placeholder="Optional epic name"
                    variant="outlined"
                  />

                  <!-- Linked Tasks Management -->
                  <div class="mt-4">
                    <h4 class="text-subtitle-2 mb-3">
                      <v-icon class="me-2">mdi-link</v-icon>
                      Linked Tasks
                    </h4>

                    <!-- Existing Linked Tasks -->
                    <div v-if="editForm.linked_tasks.length > 0" class="mb-3">
                      <v-chip
                        v-for="(linkedTask, index) in editForm.linked_tasks"
                        :key="`${linkedTask.task_id}-${index}`"
                        class="me-2 mb-2"
                        closable
                        color="primary"
                        variant="outlined"
                        @click:close="removeLinkedTask(index)"
                      >
                        <v-icon start>mdi-link-variant</v-icon>
                        {{ linkedTask.task_id }} ({{ linkedTask.linkType }})
                      </v-chip>
                    </div>

                    <!-- Add New Linked Task -->
                    <v-card class="pa-3" color="grey-lighten-5" variant="outlined">
                      <div class="text-subtitle-2 mb-2">Add Linked Task</div>
                      <v-row dense>
                        <v-col cols="12" md="6">
                          <v-autocomplete
                            v-model="newLinkedTask.task_id"
                            v-model:search="autocompleteSearch"
                            clearable
                            density="compact"
                            hide-details
                            item-title="title"
                            item-value="value"
                            :items="autocompleteItems"
                            label="Task ID"
                            :loading="autocompleteLoading"
                            no-data-text="No matching tasks found"
                            placeholder="Type to search tasks..."
                            prepend-inner-icon="mdi-identifier"
                            variant="outlined"
                            @update:model-value="onAutocompleteSelect"
                            @update:search="onAutocompleteSearch"
                          >
                            <template #item="{ props, item }">
                              <v-list-item
                                v-bind="props"
                                :prepend-icon="item.raw.props.prependIcon"
                                :subtitle="item.raw.subtitle"
                                :title="item.raw.title"
                              >
                                <template #append>
                                  <v-icon
                                    :color="item.raw.subtitle.includes('High') ? 'error' :
                                      item.raw.subtitle.includes('Low') ? 'success' : 'warning'"
                                    size="small"
                                  >
                                    {{ item.raw.props.appendIcon }}
                                  </v-icon>
                                </template>
                              </v-list-item>
                            </template>
                          </v-autocomplete>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-select
                            v-model="newLinkedTask.linkType"
                            density="compact"
                            hide-details
                            :items="linkTypes"
                            label="Link Type"
                            variant="outlined"
                          />
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-btn
                            color="primary"
                            icon="mdi-plus"
                            size="small"
                            variant="elevated"
                            @click="addLinkedTask"
                          />
                        </v-col>
                      </v-row>
                      <div class="text-caption text-medium-emphasis mt-2">
                        <strong>Parent:</strong> Linked task is a parent of this task<br>
                        <strong>Requires:</strong> This task requires the linked task to be completed
                      </div>
                    </v-card>
                  </div>
                </div>
              </div>
            </v-col>

            <v-col cols="12" md="6">
              <div class="mb-4">
                <h4 class="text-subtitle-1 font-weight-medium mb-2">Timestamps</h4>
                <v-table density="compact">
                  <tbody>
                    <tr>
                      <td class="font-weight-medium">Created</td>
                      <td>{{ formatDate(task.created_at) }}</td>
                    </tr>
                    <tr>
                      <td class="font-weight-medium">Updated</td>
                      <td>{{ formatDate(task.updated_at) }}</td>
                    </tr>
                  </tbody>
                </v-table>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>

      <!-- Linked Tasks -->
      <v-card v-if="task.linked_tasks && task.linked_tasks.length > 0" class="mb-6" elevation="2">
        <v-card-title>
          <v-icon class="me-2">mdi-link-variant</v-icon>
          Linked Tasks ({{ task.linked_tasks.length }})
        </v-card-title>
        <v-card-text>
          <div class="d-flex flex-wrap ga-2">
            <v-chip
              v-for="linkedTask in task.linked_tasks"
              :key="linkedTask.task_id"
              class="cursor-pointer"
              variant="outlined"
              @click="navigateToTask(linkedTask.task_id)"
            >
              <v-icon start>mdi-open-in-new</v-icon>
              {{ linkedTask.task_id }} ({{ linkedTask.linkType }})
            </v-chip>
          </div>
        </v-card-text>
      </v-card>

      <!-- Task Description -->
      <v-card elevation="2">
        <v-card-title>
          <v-icon class="me-2">mdi-text-box-outline</v-icon>
          Description
          <v-spacer />

          <!-- View Mode Toggle (only in view mode) -->
          <v-btn-toggle
            v-if="!isEditMode"
            v-model="viewMode"
            density="compact"
            mandatory
            variant="outlined"
          >
            <v-btn icon="mdi-eye" title="Rendered View" value="rendered" />
            <v-btn icon="mdi-code-tags" title="Markdown Source" value="markdown" />
          </v-btn-toggle>

          <!-- Edit Mode Indicator -->
          <v-chip v-else color="primary" size="small" variant="tonal">
            <v-icon start>mdi-pencil</v-icon>
            Editing
          </v-chip>
        </v-card-title>

        <v-card-text class="task-description">
          <!-- Edit Mode -->
          <div v-if="isEditMode">
            <MdEditor
              v-model="editForm.description"
              :height="400"
              language="en-US"
              placeholder="Enter task description using Markdown syntax..."
              preview-theme="vuepress"
              theme="light"
            />
          </div>

          <!-- View Mode -->
          <div v-else>
            <!-- No Description State -->
            <div v-if="!task.description" class="text-center py-8">
              <v-icon class="mb-3" color="grey-lighten-2" size="48">
                mdi-text-box-remove-outline
              </v-icon>
              <p class="text-body-1 text-medium-emphasis">
                No description provided for this task.
              </p>
            </div>

            <!-- Rendered Markdown View -->
            <div v-else-if="viewMode === 'rendered'" class="markdown-content">
              <MdPreview :model-value="task.description" preview-theme="vuepress" />
            </div>

            <!-- Raw Markdown View -->
            <div v-else class="markdown-source">
              <pre class="text-body-2"><code>{{ task.description }}</code></pre>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </div>

    <!-- Delete Confirmation Dialog -->
    <v-dialog
      v-model="showDeleteDialog"
      max-width="500"
      persistent
    >
      <v-card>
        <v-card-title class="d-flex align-center">
          <v-icon class="me-3" color="error">mdi-alert-circle</v-icon>
          <span class="text-h6">Delete Task</span>
        </v-card-title>

        <v-card-text>
          <p class="text-body-1 mb-4">
            Are you sure you want to delete this task? This action cannot be undone.
          </p>

          <v-card class="pa-3" color="grey-lighten-5" variant="outlined">
            <div class="text-subtitle-2 font-weight-medium">{{ task?.summary }}</div>
            <div class="text-body-2 text-medium-emphasis">{{ task?.task_id }}</div>
          </v-card>
        </v-card-text>

        <v-card-actions class="justify-end pa-4">
          <v-btn
            color="grey"
            :disabled="deleteLoading"
            variant="text"
            @click="closeDeleteDialog"
          >
            Cancel
          </v-btn>

          <v-btn
            color="error"
            :loading="deleteLoading"
            prepend-icon="mdi-delete"
            variant="elevated"
            @click="confirmDeleteTask"
          >
            Delete Task
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Status Update Snackbar -->
    <v-snackbar
      v-model="showSnackbar"
      :color="snackbarColor"
      timeout="3000"
    >
      {{ snackbarMessage }}
      <template #actions>
        <v-btn
          color="white"
          variant="text"
          @click="showSnackbar = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup>
  import { onMounted, ref, watch } from 'vue'
  import { MdEditor, MdPreview } from 'md-editor-v3'
  import { useRoute, useRouter } from 'vue-router'
  import { useTasksStore } from '@/stores/tasks'

  import 'md-editor-v3/lib/style.css'

  const route = useRoute()
  const router = useRouter()
  const tasksStore = useTasksStore()

  // Reactive data
  const task = ref(null)
  const loading = ref(false)
  const error = ref(null)
  const viewMode = ref('rendered')
  const showSnackbar = ref(false)
  const snackbarMessage = ref('')
  const snackbarColor = ref('success')

  // Edit mode state
  const isEditMode = ref(false)
  const editLoading = ref(false)
  const isNewTask = ref(false)

  // Delete state
  const showDeleteDialog = ref(false)
  const deleteLoading = ref(false)
  const editForm = ref({
    task_id: '',
    summary: '',
    description: '',
    type: '',
    priority: '',
    status: '',
    estimated_effort: '',
    epic: '',
    linked_tasks: [],
  })
  const originalTask = ref(null)

  // Task linking state
  const newLinkedTask = ref({
    task_id: '',
    linkType: 'Requires',
  })
  const linkTypes = ['Parent', 'Requires']

  // Autocomplete state
  const autocompleteSearch = ref('')
  const autocompleteLoading = ref(false)
  const debounceTimeout = ref(null)

  // Form validation rules
  const rules = {
    summary: [
      v => !!v || 'Summary is required',
      v => (v && v.length >= 3) || 'Summary must be at least 3 characters',
    ],
    task_id: [
      v => {
        if (isNewTask.value) {
          if (!v) return 'Task ID is required'
          const formatError = tasksStore.validateTaskId(v)
          if (formatError) return formatError
          if (tasksStore.checkTaskIdExists(v)) return 'Task ID already exists'
        }
        return true
      },
    ],
  }

  // Options for select fields
  const typeOptions = ['Story', 'Task', 'Epic', 'Bug']
  const priorityOptions = ['High', 'Medium', 'Low']
  const statusOptions = ['Backlog', 'In Progress', 'Done', 'Blocked']
  const effortOptions = ['Large', 'Medium', 'Small']

  // Computed properties for autocomplete
  const availableTasksForLinking = computed(() => {
    if (!tasksStore.tasks || tasksStore.tasks.length === 0) return []

    return tasksStore.tasks.filter(t => {
      // Exclude current task
      const currentTaskId = task.value?.task_id || editForm.value.task_id
      if (t.task_id === currentTaskId) return false

      // Exclude already linked tasks
      const alreadyLinked = editForm.value.linked_tasks.some(
        link => link.task_id === t.task_id,
      )
      if (alreadyLinked) return false

      return true
    })
  })

  const filteredTaskSuggestions = computed(() => {
    if (!autocompleteSearch.value || autocompleteSearch.value.length === 0) {
      return availableTasksForLinking.value.slice(0, 10)
    }

    const searchTerm = autocompleteSearch.value.toLowerCase()
    const filtered = availableTasksForLinking.value.filter(t => {
      return t.task_id.toLowerCase().includes(searchTerm)
        || t.summary.toLowerCase().includes(searchTerm)
    })

    // Sort by relevance: exact task ID matches first, then partial matches
    const sorted = filtered.sort((a, b) => {
      const aIdMatch = a.task_id.toLowerCase().startsWith(searchTerm)
      const bIdMatch = b.task_id.toLowerCase().startsWith(searchTerm)

      if (aIdMatch && !bIdMatch) return -1
      if (!aIdMatch && bIdMatch) return 1

      // If both or neither match task ID, sort by task ID
      return a.task_id.localeCompare(b.task_id)
    })

    return sorted.slice(0, 10)
  })

  const autocompleteItems = computed(() => {
    return filteredTaskSuggestions.value.map(t => ({
      title: `[${t.task_id}] ${t.summary}`,
      value: t.task_id,
      subtitle: `${t.type} • ${t.priority} Priority • ${t.status}`,
      props: {
        prependIcon: getTaskTypeIcon(t.type),
        appendIcon: getPriorityIcon(t.priority),
      },
    }))
  })

  // Methods
  const fetchTask = async taskId => {
    loading.value = true
    error.value = null

    try {
      const foundTask = await tasksStore.getTaskById(taskId)

      if (foundTask) {
        task.value = foundTask
      } else {
        error.value = `Task with ID "${taskId}" not found`
      }
    } catch (error_) {
      error.value = `Failed to load task: ${error_.message}`
      console.error('Error fetching task:', error_)
    } finally {
      loading.value = false
    }
  }

  const updateStatus = async newStatus => {
    if (!task.value) return

    try {
      await tasksStore.updateTaskStatus(task.value.task_id, newStatus)

      // Update local task object
      task.value.status = newStatus
      task.value.updated_at = new Date().toISOString()

      // Show success message
      snackbarMessage.value = `Task status updated to "${newStatus}"`
      snackbarColor.value = 'success'
      showSnackbar.value = true
    } catch (error) {
      snackbarMessage.value = `Failed to update task status: ${error.message}`
      snackbarColor.value = 'error'
      showSnackbar.value = true
      console.error('Failed to update task status:', error)
    }
  }

  const navigateToTask = async taskId => {
    if (taskId === task.value?.task_id) return // Don't navigate to self

    await router.push(`/tasks/${taskId}`)
  }

  const getPriorityColor = priority => {
    switch (priority) {
      case 'High': { return 'error'
      }
      case 'Medium': { return 'warning'
      }
      case 'Low': { return 'success'
      }
      default: { return 'grey'
      }
    }
  }

  const getStatusColor = status => {
    switch (status) {
      case 'Done': { return 'success'
      }
      case 'In Progress': { return 'info'
      }
      case 'Blocked': { return 'error'
      }
      case 'Backlog': { return 'grey'
      }
      default: { return 'grey'
      }
    }
  }

  const getTypeIcon = type => {
    switch (type) {
      case 'Story': { return 'mdi-book-open-page-variant'
      }
      case 'Task': { return 'mdi-checkbox-marked-circle'
      }
      case 'Epic': { return 'mdi-flag'
      }
      case 'Bug': { return 'mdi-bug'
      }
      default: { return 'mdi-file-document'
      }
    }
  }

  const formatDate = dateString => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleString()
  }

  // Helper functions for autocomplete icons
  const getTaskTypeIcon = type => {
    switch (type) {
      case 'Epic': {
        return 'mdi-flag'
      }
      case 'Story': {
        return 'mdi-book-open'
      }
      case 'Bug': {
        return 'mdi-bug'
      }
      case 'Task':
      default: {
        return 'mdi-check-circle'
      }
    }
  }

  const getPriorityIcon = priority => {
    switch (priority) {
      case 'High': {
        return 'mdi-arrow-up'
      }
      case 'Low': {
        return 'mdi-arrow-down'
      }
      case 'Medium':
      default: {
        return 'mdi-minus'
      }
    }
  }

  // Autocomplete methods
  const onAutocompleteSearch = searchInput => {
    // Clear existing timeout
    if (debounceTimeout.value) {
      clearTimeout(debounceTimeout.value)
    }

    // Set loading state
    autocompleteLoading.value = true

    // Debounce the search
    debounceTimeout.value = setTimeout(() => {
      autocompleteSearch.value = searchInput || ''
      autocompleteLoading.value = false
    }, 300)
  }

  const onAutocompleteSelect = selectedTaskId => {
    newLinkedTask.value.task_id = selectedTaskId
    autocompleteSearch.value = selectedTaskId
  }

  // Task linking methods
  const addLinkedTask = () => {
    const taskId = newLinkedTask.value.task_id.trim()
    const linkType = newLinkedTask.value.linkType

    // Validate task ID
    const validationError = tasksStore.validateLinkedTask(taskId)
    if (validationError) {
      snackbarMessage.value = validationError
      snackbarColor.value = 'error'
      showSnackbar.value = true
      return
    }

    // Check if already linked
    const alreadyLinked = editForm.value.linked_tasks.some(
      link => link.task_id === taskId,
    )
    if (alreadyLinked) {
      snackbarMessage.value = 'Task is already linked'
      snackbarColor.value = 'warning'
      showSnackbar.value = true
      return
    }

    // Check for circular dependency (prevent linking to self)
    if (taskId === task.value?.task_id || taskId === editForm.value.task_id) {
      snackbarMessage.value = 'Cannot link task to itself'
      snackbarColor.value = 'error'
      showSnackbar.value = true
      return
    }

    // Add the linked task
    editForm.value.linked_tasks.push({
      task_id: taskId,
      linkType,
    })

    // Reset form
    newLinkedTask.value = {
      task_id: '',
      linkType: 'Requires',
    }
    autocompleteSearch.value = ''

    snackbarMessage.value = 'Linked task added'
    snackbarColor.value = 'success'
    showSnackbar.value = true
  }

  const removeLinkedTask = index => {
    editForm.value.linked_tasks.splice(index, 1)
    snackbarMessage.value = 'Linked task removed'
    snackbarColor.value = 'info'
    showSnackbar.value = true
  }

  const generateNewTaskId = () => {
    editForm.value.task_id = tasksStore.generateTaskId()
  }

  // Delete methods
  const openDeleteDialog = () => {
    showDeleteDialog.value = true
  }

  const closeDeleteDialog = () => {
    showDeleteDialog.value = false
  }

  const confirmDeleteTask = async () => {
    if (!task.value || isNewTask.value) return

    deleteLoading.value = true

    try {
      const success = await tasksStore.deleteTask(task.value.task_id)

      if (success) {
        // Show success message
        snackbarMessage.value = 'Task deleted successfully'
        snackbarColor.value = 'success'
        showSnackbar.value = true

        // Close dialog
        showDeleteDialog.value = false

        // Navigate back to task list
        await router.push('/')
      } else {
        throw new Error('Failed to delete task')
      }
    } catch (error) {
      snackbarMessage.value = `Failed to delete task: ${error.message}`
      snackbarColor.value = 'error'
      showSnackbar.value = true
      console.error('Failed to delete task:', error)
    } finally {
      deleteLoading.value = false
    }
  }

  // Edit mode methods
  const enterEditMode = () => {
    if (!task.value) return

    // Store original task for cancellation
    originalTask.value = { ...task.value }

    // Populate edit form with current task data
    editForm.value = {
      task_id: task.value.task_id || '',
      summary: task.value.summary || '',
      description: task.value.description || '',
      type: task.value.type || 'Task',
      priority: task.value.priority || 'Medium',
      status: task.value.status || 'Backlog',
      estimated_effort: task.value.estimated_effort || 'Medium',
      epic: task.value.epic || '',
      linked_tasks: [...(task.value.linked_tasks || [])],
    }

    isEditMode.value = true
  }

  const cancelEdit = () => {
    if (isNewTask.value) {
      // For new tasks, navigate back to task list
      router.push('/')
    } else {
      // For existing tasks, exit edit mode
      isEditMode.value = false
      editForm.value = {
        task_id: '',
        summary: '',
        description: '',
        type: '',
        priority: '',
        status: '',
        estimated_effort: '',
        epic: '',
        linked_tasks: [],
      }
      originalTask.value = null
    }
  }

  const saveTask = async () => {
    // Validate required fields
    if (!editForm.value.summary || editForm.value.summary.length < 3) {
      snackbarMessage.value = 'Summary is required and must be at least 3 characters'
      snackbarColor.value = 'error'
      showSnackbar.value = true
      return
    }

    // Validate task ID for new tasks
    if (isNewTask.value) {
      if (!editForm.value.task_id) {
        snackbarMessage.value = 'Task ID is required'
        snackbarColor.value = 'error'
        showSnackbar.value = true
        return
      }

      const formatError = tasksStore.validateTaskId(editForm.value.task_id)
      if (formatError) {
        snackbarMessage.value = `Invalid task ID: ${formatError}`
        snackbarColor.value = 'error'
        showSnackbar.value = true
        return
      }

      if (tasksStore.checkTaskIdExists(editForm.value.task_id)) {
        snackbarMessage.value = 'Task ID already exists'
        snackbarColor.value = 'error'
        showSnackbar.value = true
        return
      }
    }

    editLoading.value = true

    try {
      if (isNewTask.value) {
        // Create new task
        const newTaskData = {
          task_id: editForm.value.task_id,
          summary: editForm.value.summary,
          description: editForm.value.description || null,
          type: editForm.value.type,
          priority: editForm.value.priority,
          status: editForm.value.status,
          estimated_effort: editForm.value.estimated_effort,
          epic: editForm.value.epic || null,
          linked_tasks: editForm.value.linked_tasks,
        }

        const createdTask = await tasksStore.addTask(newTaskData)

        if (createdTask) {
          // Show success message
          snackbarMessage.value = 'Task created successfully'
          snackbarColor.value = 'success'
          showSnackbar.value = true

          // Redirect to the new task's detail page
          await router.push(`/tasks/${createdTask.task_id}`)
        } else {
          throw new Error('Failed to create task')
        }
      } else {
        // Update existing task
        const updateData = {
          summary: editForm.value.summary,
          description: editForm.value.description || null,
          type: editForm.value.type,
          priority: editForm.value.priority,
          status: editForm.value.status,
          estimated_effort: editForm.value.estimated_effort,
          epic: editForm.value.epic || null,
          linked_tasks: editForm.value.linked_tasks,
        }

        const updatedTask = await tasksStore.updateTask(task.value.task_id, updateData)

        if (updatedTask) {
          // Update local task object
          task.value = { ...task.value, ...updatedTask }

          // Exit edit mode
          isEditMode.value = false
          originalTask.value = null

          // Show success message
          snackbarMessage.value = 'Task updated successfully'
          snackbarColor.value = 'success'
          showSnackbar.value = true
        } else {
          throw new Error('Failed to update task')
        }
      }
    } catch (error) {
      const action = isNewTask.value ? 'create' : 'update'
      snackbarMessage.value = `Failed to ${action} task: ${error.message}`
      snackbarColor.value = 'error'
      showSnackbar.value = true
      console.error(`Failed to ${action} task:`, error)
    } finally {
      editLoading.value = false
    }
  }

  // Initialize new task
  const initializeNewTask = () => {
    isNewTask.value = true
    isEditMode.value = true

    // Create a temporary task object for new task
    task.value = {
      task_id: 'NEW-TASK',
      summary: '',
      description: '',
      type: 'Task',
      priority: 'Medium',
      status: 'Backlog',
      estimated_effort: 'Medium',
      epic: '',
      linked_tasks: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    // Populate edit form with default values
    editForm.value = {
      task_id: '',
      summary: '',
      description: '',
      type: 'Task',
      priority: 'Medium',
      status: 'Backlog',
      estimated_effort: 'Medium',
      epic: '',
      linked_tasks: [],
    }
  }

  // Lifecycle
  onMounted(() => {
    const taskId = route.params.id
    if (taskId === 'new') {
      initializeNewTask()
    } else if (taskId) {
      fetchTask(taskId)
    }
  })

  // Watch for route changes
  watch(() => route.params.id, newId => {
    if (newId === 'new') {
      initializeNewTask()
    } else if (newId) {
      fetchTask(newId)
    }
  })
</script>

<style scoped>
.task-description {
  max-width: none;
}

.markdown-content {
  line-height: 1.6;
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.markdown-content :deep(h1) {
  font-size: 2rem;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 0.5rem;
}

.markdown-content :deep(h2) {
  font-size: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0.3rem;
}

.markdown-content :deep(p) {
  margin-bottom: 1rem;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin-bottom: 1rem;
  padding-left: 2rem;
}

.markdown-content :deep(li) {
  margin-bottom: 0.25rem;
}

.markdown-content :deep(blockquote) {
  border-left: 4px solid #2196f3;
  margin: 1rem 0;
  padding: 0.5rem 1rem;
  background-color: #f5f5f5;
  font-style: italic;
}

.markdown-content :deep(code) {
  background-color: #f5f5f5;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.markdown-content :deep(pre) {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 5px;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-content :deep(pre code) {
  background-color: transparent;
  padding: 0;
}

.markdown-content :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  border: 1px solid #ddd;
  padding: 0.5rem;
  text-align: left;
}

.markdown-content :deep(th) {
  background-color: #f5f5f5;
  font-weight: 600;
}

.markdown-content :deep(a) {
  color: #2196f3;
  text-decoration: none;
}

.markdown-content :deep(a:hover) {
  text-decoration: underline;
}

.markdown-source {
  background-color: #f8f8f8;
  border-radius: 5px;
  padding: 1rem;
}

.markdown-source pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.markdown-source code {
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  line-height: 1.4;
}

.cursor-pointer {
  cursor: pointer;
}

.markdown-preview {
  max-height: 300px;
  overflow-y: auto;
}

.markdown-preview .markdown-content {
  line-height: 1.6;
}

.markdown-preview .markdown-content :deep(h1),
.markdown-preview .markdown-content :deep(h2),
.markdown-preview .markdown-content :deep(h3),
.markdown-preview .markdown-content :deep(h4),
.markdown-preview .markdown-content :deep(h5),
.markdown-preview .markdown-content :deep(h6) {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.markdown-preview .markdown-content :deep(p) {
  margin-bottom: 0.5rem;
}

.markdown-preview .markdown-content :deep(ul),
.markdown-preview .markdown-content :deep(ol) {
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
}

.markdown-preview .markdown-content :deep(code) {
  background-color: #f5f5f5;
  padding: 0.1rem 0.3rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.markdown-preview .markdown-content :deep(pre) {
  background-color: #f5f5f5;
  padding: 0.5rem;
  border-radius: 5px;
  overflow-x: auto;
  margin: 0.5rem 0;
}

.markdown-preview .markdown-content :deep(blockquote) {
  border-left: 4px solid #2196f3;
  margin: 0.5rem 0;
  padding: 0.25rem 0.5rem;
  background-color: #f5f5f5;
  font-style: italic;
}
</style>
