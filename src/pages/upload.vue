<template>
  <v-container class="pa-6" fluid>
    <!-- <PERSON> Header -->
    <v-row class="mb-6">
      <v-col>
        <div class="d-flex align-center justify-space-between">
          <div>
            <h1 class="text-h3 font-weight-bold mb-2">Upload Tasks</h1>
            <p class="text-subtitle-1 text-medium-emphasis">
              Import your project tasks from JSON files
            </p>
          </div>
          <v-chip
            v-if="tasksStore.tasks.length > 0"
            color="primary"
            size="large"
            variant="elevated"
          >
            {{ tasksStore.tasks.length }} Tasks in Database
          </v-chip>
        </div>
      </v-col>
    </v-row>

    <!-- Info Card -->
    <TaskUploadInfo />

    <!-- File Upload Section -->
    <v-row class="mb-6">
      <v-col>
        <v-card class="mb-4" elevation="2">
          <v-card-title class="d-flex align-center">
            <v-icon class="me-3" color="primary">mdi-upload</v-icon>
            Upload Tasks JSON File
          </v-card-title>

          <v-card-text>
            <div class="text-center pa-6">
              <v-file-input
                v-model="selectedFile"
                accept=".json"
                class="mb-4"
                :disabled="tasksStore.uploadStatus === 'processing'"
                label="Select JSON file"
                :loading="tasksStore.loading"
                prepend-icon="mdi-file-document"
                variant="outlined"
                @change="handleFileSelect"
              />

              <!-- Upload Progress -->
              <div v-if="tasksStore.uploadStatus === 'processing'" class="mb-4">
                <v-progress-linear
                  color="primary"
                  height="8"
                  :model-value="tasksStore.uploadProgress"
                  rounded
                />
                <p class="text-caption mt-2">Processing tasks...</p>
              </div>

              <!-- Upload Button -->
              <v-btn
                color="primary"
                :disabled="!selectedFile || tasksStore.uploadStatus === 'processing'"
                :loading="tasksStore.uploadStatus === 'processing'"
                size="large"
                variant="elevated"
                @click="processUpload"
              >
                <v-icon start>mdi-database-import</v-icon>
                Process Upload
              </v-btn>

              <!-- Clear Database Button -->
              <v-btn
                v-if="tasksStore.tasks.length > 0"
                class="ml-4"
                color="error"
                variant="outlined"
                @click="showClearDialog = true"
              >
                <v-icon start>mdi-delete</v-icon>
                Clear Database
              </v-btn>
            </div>

            <!-- Upload Result -->
            <v-alert
              v-if="tasksStore.uploadStatus === 'success' && tasksStore.lastUploadResult"
              class="mt-4"
              type="success"
              variant="tonal"
            >
              <v-alert-title>Upload Successful!</v-alert-title>
              Successfully imported {{ tasksStore.lastUploadResult.successful }} tasks.
              <div v-if="tasksStore.lastUploadResult.failed > 0" class="mt-2">
                <strong>{{ tasksStore.lastUploadResult.failed }} tasks failed:</strong>
                <ul class="mt-1">
                  <li v-for="error in tasksStore.lastUploadResult.errors" :key="error">
                    {{ error }}
                  </li>
                </ul>
              </div>
            </v-alert>

            <!-- Error Display -->
            <v-alert
              v-if="tasksStore.error"
              class="mt-4"
              closable
              type="error"
              variant="tonal"
              @click:close="tasksStore.error = null"
            >
              {{ tasksStore.error }}
            </v-alert>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Statistics Cards -->
    <v-row v-if="tasksStore.tasks.length > 0" class="mb-6">
      <v-col cols="12" md="3">
        <v-card color="primary" variant="elevated">
          <v-card-text class="text-center">
            <v-icon class="mb-2" size="48">mdi-format-list-checks</v-icon>
            <div class="text-h4 font-weight-bold">{{ tasksStore.statistics.total }}</div>
            <div class="text-subtitle-1">Total Tasks</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="3">
        <v-card color="success" variant="elevated">
          <v-card-text class="text-center">
            <v-icon class="mb-2" size="48">mdi-check-circle</v-icon>
            <div class="text-h4 font-weight-bold">
              {{ tasksStore.tasksByStatus.Done?.length || 0 }}
            </div>
            <div class="text-subtitle-1">Completed</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="3">
        <v-card color="warning" variant="elevated">
          <v-card-text class="text-center">
            <v-icon class="mb-2" size="48">mdi-clock-outline</v-icon>
            <div class="text-h4 font-weight-bold">
              {{ tasksStore.tasksByStatus['In Progress']?.length || 0 }}
            </div>
            <div class="text-subtitle-1">In Progress</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="3">
        <v-card color="info" variant="elevated">
          <v-card-text class="text-center">
            <v-icon class="mb-2" size="48">mdi-playlist-plus</v-icon>
            <div class="text-h4 font-weight-bold">
              {{ tasksStore.tasksByStatus.Backlog?.length || 0 }}
            </div>
            <div class="text-subtitle-1">Backlog</div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Success Actions -->
    <v-row v-if="tasksStore.uploadStatus === 'success'">
      <v-col>
        <v-card class="text-center pa-6" elevation="1">
          <v-icon class="mb-4" color="success" size="64">
            mdi-check-circle
          </v-icon>
          <h2 class="text-h5 mb-4">Tasks Uploaded Successfully!</h2>
          <p class="text-body-1 text-medium-emphasis mb-6">
            Your tasks have been imported and are ready to manage.
          </p>
          <v-btn
            color="primary"
            size="large"
            variant="elevated"
            @click="$router.push('/')"
          >
            <v-icon start>mdi-format-list-checks</v-icon>
            View Tasks
          </v-btn>
        </v-card>
      </v-col>
    </v-row>

    <!-- Empty State -->
    <v-row v-else-if="tasksStore.tasks.length === 0 && !tasksStore.loading && tasksStore.uploadStatus === 'idle'">
      <v-col>
        <v-card class="text-center pa-12" elevation="1">
          <v-icon class="mb-4" color="grey-lighten-2" size="120">
            mdi-file-upload-outline
          </v-icon>
          <h2 class="text-h5 mb-4">Ready to Import Tasks</h2>
          <p class="text-body-1 text-medium-emphasis mb-6">
            Upload a JSON file containing your tasks to get started with task management.
          </p>
        </v-card>
      </v-col>
    </v-row>

    <!-- Clear Database Confirmation Dialog -->
    <v-dialog v-model="showClearDialog" max-width="500">
      <v-card>
        <v-card-title class="d-flex align-center">
          <v-icon class="me-3" color="error">mdi-alert</v-icon>
          Clear Database
        </v-card-title>

        <v-card-text>
          Are you sure you want to clear all tasks from the database? This action cannot be undone.
        </v-card-text>

        <v-card-actions>
          <v-spacer />
          <v-btn @click="showClearDialog = false">Cancel</v-btn>
          <v-btn color="error" @click="clearDatabase">Clear All</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Loading Overlay -->
    <v-overlay
      v-model="tasksStore.loading"
      class="align-center justify-center"
    >
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      />
    </v-overlay>
  </v-container>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import TaskUploadInfo from '@/components/TaskUploadInfo.vue'
  import { useTasksStore } from '@/stores/tasks'

  // Store
  const tasksStore = useTasksStore()

  // Reactive data
  const selectedFile = ref(null)
  const showClearDialog = ref(false)

  // Methods
  const handleFileSelect = () => {
    tasksStore.resetUploadState()
  }

  const processUpload = async () => {
    if (!selectedFile.value) return

    try {
      const fileContent = await readFileAsText(selectedFile.value)
      const jsonData = JSON.parse(fileContent)

      await tasksStore.uploadTasksFromJson(jsonData)

      // Clear file input
      selectedFile.value = null
    } catch (error) {
      tasksStore.error = error instanceof SyntaxError ? 'Invalid JSON file. Please check the file format.' : error.message
      tasksStore.uploadStatus = 'error'
    }
  }

  const readFileAsText = async file => {
    try {
      return await file.text()
    } catch {
      throw new Error('Failed to read file')
    }
  }

  const clearDatabase = async () => {
    try {
      await tasksStore.clearAllTasks()
      showClearDialog.value = false
    } catch (error) {
      console.error('Failed to clear database:', error)
    }
  }

  // Lifecycle
  onMounted(() => {
    tasksStore.fetchTasks()
  })
</script>

<style scoped>
.v-card {
  transition: all 0.3s ease;
}

.v-card:hover {
  transform: translateY(-2px);
}
</style>
