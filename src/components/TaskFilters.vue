<template>
  <v-card class="mb-4" elevation="2">
    <v-card-title class="d-flex align-center">
      <v-icon class="me-3">mdi-filter-variant</v-icon>
      Filters & Search
      <v-spacer />
      <v-btn
        v-if="hasActiveFilters"
        size="small"
        variant="outlined"
        @click="clearAllFilters"
      >
        Clear All
      </v-btn>
    </v-card-title>

    <v-card-text>
      <!-- Search Bar -->
      <v-row class="mb-4">
        <v-col cols="12">
          <v-text-field
            v-model="searchQuery"
            clearable
            hide-details
            label="Search tasks..."
            prepend-inner-icon="mdi-magnify"
            variant="outlined"
            @update:model-value="updateSearch"
          >
            <template #append-inner>
              <v-badge
                v-if="searchQuery"
                color="primary"
                :content="searchResultCount"
              >
                <v-icon>mdi-text-search</v-icon>
              </v-badge>
            </template>
          </v-text-field>
        </v-col>
      </v-row>

      <!-- Filter Chips Row -->
      <v-row class="mb-4">
        <v-col cols="12">
          <div class="d-flex flex-wrap ga-2 align-center">
            <span class="text-body-2 text-medium-emphasis me-2">Quick Filters:</span>

            <!-- Status Quick Filters -->
            <v-chip-group
              v-model="selectedStatusFilter"
              filter
              mandatory
              :multiple="false"
              @update:model-value="updateStatusFilter"
            >
              <v-chip
                v-for="status in quickStatusFilters"
                :key="status.value"
                :value="status.value"
                :color="status.color"
                :variant="filters.status === status.value ? 'flat' : 'outlined'"
                size="small"
              >
                <v-icon start>{{ status.icon }}</v-icon>
                {{ status.label }}
                <v-badge
                  v-if="getStatusCount(status.value) > 0"
                  color="white"
                  :content="getStatusCount(status.value)"
                  inline
                  text-color="black"
                />
              </v-chip>

              <!-- Add an "All" chip for better UX -->
              <v-chip
                value="All"
                color="primary"
                :variant="filters.status === 'All' ? 'flat' : 'outlined'"
                size="small"
              >
                <v-icon start>mdi-filter-remove</v-icon>
                All
                <v-badge
                  color="white"
                  :content="totalCount"
                  inline
                  text-color="black"
                />
              </v-chip>
            </v-chip-group>
          </div>
        </v-col>
      </v-row>

      <!-- Advanced Filters -->
      <v-expansion-panels v-model="expandedPanel" variant="accordion">
        <v-expansion-panel title="Advanced Filters">
          <v-expansion-panel-text>
            <v-row>
              <!-- Type Filter -->
              <v-col cols="12" md="3">
                <v-select
                  v-model="filters.type"
                  hide-details
                  :items="typeOptions"
                  label="Type"
                  variant="outlined"
                  @update:model-value="updateFilters"
                >
                  <template #selection="{ item }">
                    <div class="d-flex align-center">
                      <v-icon class="me-2">{{ getTypeIcon(item.value) }}</v-icon>
                      {{ item.title }}
                    </div>
                  </template>
                  <template #item="{ props, item }">
                    <v-list-item v-bind="props">
                      <template #prepend>
                        <v-icon>{{ getTypeIcon(item.value) }}</v-icon>
                      </template>
                    </v-list-item>
                  </template>
                </v-select>
              </v-col>

              <!-- Priority Filter -->
              <v-col cols="12" md="3">
                <v-select
                  v-model="filters.priority"
                  hide-details
                  :items="priorityOptions"
                  label="Priority"
                  variant="outlined"
                  @update:model-value="updateFilters"
                >
                  <template #selection="{ item }">
                    <div class="d-flex align-center">
                      <v-chip class="me-2" :color="getPriorityColor(item.value)" size="x-small">
                        {{ item.title }}
                      </v-chip>
                    </div>
                  </template>
                  <template #item="{ props, item }">
                    <v-list-item v-bind="props">
                      <template #prepend>
                        <v-chip :color="getPriorityColor(item.value)" size="x-small">
                          {{ item.title }}
                        </v-chip>
                      </template>
                    </v-list-item>
                  </template>
                </v-select>
              </v-col>

              <!-- Status Filter -->
              <v-col cols="12" md="3">
                <v-select
                  v-model="filters.status"
                  hide-details
                  :items="statusOptions"
                  label="Status"
                  variant="outlined"
                  @update:model-value="updateFilters"
                >
                  <template #selection="{ item }">
                    <div class="d-flex align-center">
                      <v-chip class="me-2" :color="getStatusColor(item.value)" size="x-small" variant="tonal">
                        {{ item.title }}
                      </v-chip>
                    </div>
                  </template>
                  <template #item="{ props, item }">
                    <v-list-item v-bind="props">
                      <template #prepend>
                        <v-chip :color="getStatusColor(item.value)" size="x-small" variant="tonal">
                          {{ item.title }}
                        </v-chip>
                      </template>
                    </v-list-item>
                  </template>
                </v-select>
              </v-col>

              <!-- Epic Filter -->
              <v-col cols="12" md="3">
                <v-select
                  v-model="filters.epic"
                  hide-details
                  :items="epicOptions"
                  label="Epic"
                  variant="outlined"
                  @update:model-value="updateFilters"
                >
                  <template #selection="{ item }">
                    <div class="d-flex align-center">
                      <v-icon class="me-2" color="primary">mdi-flag</v-icon>
                      {{ item.title }}
                    </div>
                  </template>
                  <template #item="{ props, item }">
                    <v-list-item v-bind="props">
                      <template #prepend>
                        <v-icon color="primary">mdi-flag</v-icon>
                      </template>
                    </v-list-item>
                  </template>
                </v-select>
              </v-col>
            </v-row>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>

      <!-- Active Filters Display -->
      <div v-if="hasActiveFilters" class="mt-4">
        <div class="d-flex align-center flex-wrap ga-2">
          <span class="text-body-2 text-medium-emphasis">Active filters:</span>

          <v-chip
            v-if="filters.type !== 'All'"
            closable
            :color="getTypeColor(filters.type)"
            size="small"
            @click:close="filters.type = 'All'; updateFilters()"
          >
            <v-icon start>{{ getTypeIcon(filters.type) }}</v-icon>
            Type: {{ filters.type }}
          </v-chip>

          <v-chip
            v-if="filters.priority !== 'All'"
            closable
            :color="getPriorityColor(filters.priority)"
            size="small"
            @click:close="filters.priority = 'All'; updateFilters()"
          >
            Priority: {{ filters.priority }}
          </v-chip>

          <v-chip
            v-if="filters.status !== 'All'"
            closable
            :color="getStatusColor(filters.status)"
            size="small"
            variant="tonal"
            @click:close="filters.status = 'All'; updateFilters()"
          >
            Status: {{ filters.status }}
          </v-chip>

          <v-chip
            v-if="filters.epic !== 'All'"
            closable
            color="primary"
            size="small"
            variant="outlined"
            @click:close="filters.epic = 'All'; updateFilters()"
          >
            <v-icon start>mdi-flag</v-icon>
            Epic: {{ filters.epic }}
          </v-chip>
        </div>
      </div>

      <!-- Results Summary -->
      <div class="mt-4 d-flex justify-space-between align-center">
        <div class="text-body-2 text-medium-emphasis">
          Showing {{ resultCount }} of {{ totalCount }} tasks
        </div>

        <div class="d-flex align-center ga-2">
          <span class="text-body-2 text-medium-emphasis">View:</span>
          <v-btn-toggle v-model="viewMode" density="compact" mandatory variant="outlined">
            <v-btn icon="mdi-view-list" value="list" />
            <v-btn icon="mdi-view-grid" value="grid" />
          </v-btn-toggle>
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script setup>
  import { computed, ref, watch } from 'vue'

  // Props
  const props = defineProps({
    tasks: {
      type: Array,
      default: () => [],
    },
    modelValue: {
      type: Object,
      default: () => ({
        search: '',
        type: 'All',
        priority: 'All',
        status: 'All',
        epic: 'All',
      }),
    },
    viewMode: {
      type: String,
      default: 'list',
    },
    totalTasksCount: {
      type: Number,
      default: 0,
    },
  })

  // Emits
  const emit = defineEmits(['update:modelValue', 'update:viewMode'])

  // Local state
  const searchQuery = ref(props.modelValue.search || '')
  // Initialize selectedStatusFilter based on current status filter
  const selectedStatusFilter = ref(
    props.modelValue.status !== 'All' ? [props.modelValue.status] : []
  )
  const expandedPanel = ref([])
  const viewMode = ref(props.viewMode)

  const filters = ref({
    type: props.modelValue.type || 'All',
    priority: props.modelValue.priority || 'All',
    status: props.modelValue.status || 'All',
    epic: props.modelValue.epic || 'All',
  })

  // Add a watcher to keep selectedStatusFilter in sync with filters.status
  watch(
    () => filters.value.status,
    (newStatus) => {
      if (newStatus === 'All') {
        selectedStatusFilter.value = []
      } else if (!selectedStatusFilter.value.includes(newStatus)) {
        selectedStatusFilter.value = [newStatus]
      }
    }
  )

  // Also watch for external changes to modelValue
  watch(
    () => props.modelValue.status,
    (newStatus) => {
      // Update local filters
      filters.value.status = newStatus || 'All'

      // Update chip selection
      if (newStatus === 'All') {
        selectedStatusFilter.value = []
      } else {
        selectedStatusFilter.value = [newStatus]
      }
    }
  )

  // Watch for changes in modelValue.search to update searchQuery
  watch(() => props.modelValue.search, newSearch => {
    searchQuery.value = newSearch
  })

  // Computed properties
  const typeOptions = computed(() => {
    const types = ['All', ...new Set(props.tasks.map(task => task.type))]
    return types.map(type => ({ title: type, value: type }))
  })

  const priorityOptions = computed(() => {
    const priorities = ['All', ...new Set(props.tasks.map(task => task.priority))]
    return priorities.map(priority => ({ title: priority, value: priority }))
  })

  const statusOptions = computed(() => {
    const statuses = ['All', ...new Set(props.tasks.map(task => task.status))]
    return statuses.map(status => ({ title: status, value: status }))
  })

  const epicOptions = computed(() => {
    const epics = ['All', ...new Set(props.tasks.map(task => task.epic).filter(Boolean))]
    return epics.map(epic => ({ title: epic, value: epic }))
  })

  const quickStatusFilters = computed(() => [
    {
      label: 'Backlog',
      value: 'Backlog',
      color: 'grey',
      icon: 'mdi-playlist-plus',
    },
    {
      label: 'In Progress',
      value: 'In Progress', // Ensure exact match with database values
      color: 'info',
      icon: 'mdi-clock-outline',
    },
    {
      label: 'Done',
      value: 'Done',
      color: 'success',
      icon: 'mdi-check-circle',
    },
    {
      label: 'Blocked',
      value: 'Blocked',
      color: 'error',
      icon: 'mdi-block-helper',
    },
  ])

  const hasActiveFilters = computed(() => {
    return searchQuery.value
      || filters.value.type !== 'All'
      || filters.value.priority !== 'All'
      || filters.value.status !== 'All'
      || filters.value.epic !== 'All'
  })

  const resultCount = computed(() => {
    let filtered = props.tasks

    if (searchQuery.value) {
      const searchTerm = searchQuery.value.toLowerCase()
      filtered = filtered.filter(task =>
        task.summary.toLowerCase().includes(searchTerm)
        || task.description?.toLowerCase().includes(searchTerm)
        || task.epic?.toLowerCase().includes(searchTerm)
        || task.id.toLowerCase().includes(searchTerm),
      )
    }

    if (filters.value.type !== 'All') {
      filtered = filtered.filter(task => task.type === filters.value.type)
    }
    if (filters.value.priority !== 'All') {
      filtered = filtered.filter(task => task.priority === filters.value.priority)
    }
    if (filters.value.status !== 'All') {
      filtered = filtered.filter(task => task.status === filters.value.status)
    }
    if (filters.value.epic !== 'All') {
      filtered = filtered.filter(task => task.epic === filters.value.epic)
    }

    return filtered.length
  })

  const totalCount = computed(() => props.tasks.length)

  const searchResultCount = computed(() => {
    if (!searchQuery.value) return 0
    const searchTerm = searchQuery.value.toLowerCase()
    return props.tasks.filter(task =>
      task.summary.toLowerCase().includes(searchTerm)
      || task.description?.toLowerCase().includes(searchTerm)
      || task.epic?.toLowerCase().includes(searchTerm)
      || task.id.toLowerCase().includes(searchTerm),
    ).length
  })

  // Methods
  const updateSearch = () => {
    emitFilters()
  }

  const updateFilters = () => {
    emitFilters()
  }

  const updateStatusFilter = selected => {
    // Handle the case where "All" is selected
    if (selected === 'All') {
      filters.value.status = 'All'
    } else if (selected) {
      filters.value.status = selected
    } else {
      // Default to "All" if nothing is selected
      filters.value.status = 'All'
    }

    console.log('Status filter updated to:', filters.value.status)
    updateFilters()
  }

  const emitFilters = () => {
    const filterData = {
      search: searchQuery.value,
      type: filters.value.type,
      priority: filters.value.priority,
      status: filters.value.status,
      epic: filters.value.epic,
    }
    console.log('Emitting filters:', filterData)
    emit('update:modelValue', filterData)
  }

  const clearAllFilters = () => {
    searchQuery.value = ''
    selectedStatusFilter.value = []
    filters.value = {
      type: 'All',
      priority: 'All',
      status: 'All',
      epic: 'All',
    }
    updateFilters()
  }

  const getStatusCount = status => {
    const count = props.tasks.filter(task => task.status === status).length
    console.log(`Status count for "${status}": ${count}`)
    return count
  }

  const getPriorityColor = priority => {
    switch (priority) {
      case 'High': { return 'error'
      }
      case 'Medium': { return 'warning'
      }
      case 'Low': { return 'success'
      }
      default: { return 'grey'
      }
    }
  }

  const getStatusColor = status => {
    switch (status) {
      case 'Done': { return 'success'
      }
      case 'In Progress': { return 'info'
      }
      case 'Blocked': { return 'error'
      }
      case 'Backlog': { return 'grey'
      }
      default: { return 'grey'
      }
    }
  }

  const getTypeIcon = type => {
    switch (type) {
      case 'Story': { return 'mdi-book-open-page-variant'
      }
      case 'Task': { return 'mdi-checkbox-marked-circle'
      }
      case 'Epic': { return 'mdi-flag'
      }
      case 'Bug': { return 'mdi-bug'
      }
      default: { return 'mdi-file-document'
      }
    }
  }

  const getTypeColor = type => {
    switch (type) {
      case 'Story': { return 'primary'
      }
      case 'Task': { return 'success'
      }
      case 'Epic': { return 'warning'
      }
      case 'Bug': { return 'error'
      }
      default: { return 'grey'
      }
    }
  }

  // Watch for view mode changes
  watch(() => props.viewMode, newMode => {
    viewMode.value = newMode
  })

  watch(viewMode, newMode => {
    emit('update:viewMode', newMode)
  })

  // Add a watcher to detect changes in the tasks array
  watch(
    () => props.tasks,
    () => {
      // Re-emit filters when tasks change to ensure filter results are updated
      emitFilters()
    },
    { deep: true },
  )
</script>

<style scoped>
.v-chip-group {
  max-width: 100%;
}
</style>
