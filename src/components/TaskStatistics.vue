<template>
  <v-card color="surface-variant" elevation="2" class="mt-4">
    <v-card-title class="d-flex align-center">
      <v-icon class="me-3">mdi-chart-line</v-icon>
      Database Statistics
    </v-card-title>
    
    <v-card-text v-if="loading">
      <div class="text-center py-4">
        <v-progress-circular indeterminate color="primary" />
        <p class="mt-2">Loading statistics...</p>
      </div>
    </v-card-text>
    
    <v-card-text v-else-if="tasksStore.tasks.length > 0">
      <v-row>
        <v-col cols="6" md="3">
          <div class="text-center">
            <div class="text-h4 font-weight-bold text-primary">
              {{ tasksStore.statistics.total }}
            </div>
            <div class="text-caption">Total Tasks</div>
          </div>
        </v-col>
        
        <v-col cols="6" md="3">
          <div class="text-center">
            <div class="text-h4 font-weight-bold text-success">
              {{ completedCount }}
            </div>
            <div class="text-caption">Completed</div>
          </div>
        </v-col>
        
        <v-col cols="6" md="3">
          <div class="text-center">
            <div class="text-h4 font-weight-bold text-warning">
              {{ inProgressCount }}
            </div>
            <div class="text-caption">In Progress</div>
          </div>
        </v-col>
        
        <v-col cols="6" md="3">
          <div class="text-center">
            <div class="text-h4 font-weight-bold text-info">
              {{ backlogCount }}
            </div>
            <div class="text-caption">Backlog</div>
          </div>
        </v-col>
      </v-row>
      
      <v-divider class="my-4" />
      
      <div class="d-flex align-center justify-space-between">
        <span class="text-body-2">
          Last updated: {{ lastUpdated }}
        </span>
        <v-btn
          size="small"
          variant="outlined"
          @click="refreshStats"
          :loading="loading"
        >
          <v-icon start>mdi-refresh</v-icon>
          Refresh
        </v-btn>
      </div>
    </v-card-text>
    
    <v-card-text v-else class="text-center py-8">
      <v-icon size="64" color="grey-lighten-2" class="mb-4">
        mdi-database-outline
      </v-icon>
      <p class="text-body-1 text-medium-emphasis">
        No tasks in database yet. Upload a JSON file to get started!
      </p>
      <v-btn 
        color="primary" 
        class="mt-4"
        @click="$router.push('/tasks')"
      >
        Upload Tasks
      </v-btn>
    </v-card-text>
  </v-card>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTasksStore } from '@/stores/tasks'

const $router = useRouter()
const tasksStore = useTasksStore()

const loading = ref(false)
const lastUpdated = ref('')

const completedCount = computed(() => {
  return tasksStore.tasksByStatus.Done?.length || 0
})

const inProgressCount = computed(() => {
  return tasksStore.tasksByStatus['In Progress']?.length || 0
})

const backlogCount = computed(() => {
  return tasksStore.tasksByStatus.Backlog?.length || 0
})

const refreshStats = async () => {
  loading.value = true
  try {
    await tasksStore.fetchTasks()
    lastUpdated.value = new Date().toLocaleString()
  } catch (error) {
    console.error('Failed to refresh statistics:', error)
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  await refreshStats()
})
</script>
