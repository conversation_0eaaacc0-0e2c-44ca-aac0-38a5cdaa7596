<template>
  <v-card color="info" variant="tonal" class="mb-4">
    <v-card-title class="d-flex align-center">
      <v-icon class="me-3">mdi-information</v-icon>
      How to Use Task Manager
    </v-card-title>
    
    <v-card-text>
      <v-expansion-panels variant="accordion">
        <v-expansion-panel title="JSON File Format">
          <v-expansion-panel-text>
            <p class="mb-3">Your JSON file should contain an array of task objects:</p>
            <pre class="text-caption bg-grey-lighten-4 pa-3 rounded">{{ jsonExample }}</pre>
            <p class="text-caption mt-2">
              <strong>Required:</strong> id, summary<br>
              <strong>Optional:</strong> description, linked_tasks, epic, priority, estimated_effort, type
            </p>
          </v-expansion-panel-text>
        </v-expansion-panel>
        
        <v-expansion-panel title="Example Files">
          <v-expansion-panel-text>
            <div class="d-flex flex-column ga-2">
              <v-btn
                variant="outlined"
                size="small"
                prepend-icon="mdi-download"
                @click="downloadExample"
              >
                Download Example Tasks
              </v-btn>
              <p class="text-caption">
                Or use the existing <code>data/backlog.json</code> file in your project for a comprehensive example.
              </p>
            </div>
          </v-expansion-panel-text>
        </v-expansion-panel>
        
        <v-expansion-panel title="Supported Task Types">
          <v-expansion-panel-text>
            <div class="d-flex flex-wrap ga-2">
              <v-chip size="small" color="primary">Story</v-chip>
              <v-chip size="small" color="success">Task</v-chip>
              <v-chip size="small" color="warning">Epic</v-chip>
              <v-chip size="small" color="error">Bug</v-chip>
            </div>
            <p class="text-caption mt-2">
              Priorities: High, Medium, Low<br>
              Effort: Large, Medium, Small
            </p>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>
    </v-card-text>
  </v-card>
</template>

<script setup>
import { ref } from 'vue'

const jsonExample = ref(`[
  {
    "id": "TASK-001",
    "summary": "Implement user login",
    "description": "Create login form with validation",
    "linked_tasks": ["TASK-002"],
    "epic": "Authentication",
    "priority": "High",
    "estimated_effort": "Medium",
    "type": "Story"
  }
]`)

const downloadExample = () => {
  const exampleData = [
    {
      "id": "EXAMPLE-001",
      "summary": "Set up project repository",
      "description": "Initialize a new project repository with proper folder structure, README, and initial configuration files.",
      "linked_tasks": [],
      "epic": "Project Setup",
      "priority": "High",
      "estimated_effort": "Small",
      "type": "Task"
    },
    {
      "id": "EXAMPLE-002", 
      "summary": "Design user interface mockups",
      "description": "Create wireframes and mockups for the main user interface screens including dashboard, forms, and navigation.",
      "linked_tasks": ["EXAMPLE-001"],
      "epic": "UI/UX Design",
      "priority": "Medium",
      "estimated_effort": "Large",
      "type": "Story"
    },
    {
      "id": "EXAMPLE-003",
      "summary": "Implement user authentication",
      "description": "Build user registration, login, and authentication system with proper security measures.",
      "linked_tasks": ["EXAMPLE-002"],
      "epic": "Authentication",
      "priority": "High",
      "estimated_effort": "Medium",
      "type": "Story"
    }
  ]
  
  const blob = new Blob([JSON.stringify(exampleData, null, 2)], { 
    type: 'application/json' 
  })
  
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = 'example-tasks.json'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}
</script>

<style scoped>
pre {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  overflow-x: auto;
  white-space: pre-wrap;
}

code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}
</style>
